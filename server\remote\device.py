# 获取设备列表
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
import json
import os
from dataclasses import dataclass
from typing import Dict
import re
import requests
import yaml
import logging

from config_file import config
from server.utils.logger import setup_logging

# 配置日志
setup_logging(),
logger = logging.getLogger(__name__)


@dataclass
class APIConfig:
    """API配置类"""
    host: str
    port: str
    token: str
    base_url: str = None

    def __post_init__(self):
        self.base_url = f"http://{self.host}:{self.port}/env-api"


# 加载配置并创建API配置实例
# config = load_config()
api_config = APIConfig(
    host=config.env['api']['development']['host'],
    port=config.env['api']['development']['port'],
    token=config.env['api']['development']['token']
)


def get_headers() -> Dict:
    return {
        "Accept": "application/json",
        "Authorization": api_config.token,
        "Connection": "keep-alive",
    }


def get_device_list(page_num: int = 1, page_size: int = 100, main_type: str = "cam") -> Dict:
    """获取设备列表

    Args:
        page_num: 页码
        page_size: 每页数量
        main_type: 设备主类型

    Returns:
        Dict: API响应数据
    """
    try:
        payload = {
            "device_item": {
                "main_type": main_type
            }
        }

        response = requests.post(
            f"{api_config.base_url}/device/list",
            headers=get_headers(),
            params={"page_num": page_num, "page_size": page_size},
            json=payload  # 添加 json 参数发送请求体
        )
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return {}

# 获取机器人状态
def get_robot_status(robot_id: str = None) -> Dict:
    """获取机器人及相关设备状态

    Args:
        robot_id: 机器人ID，如果提供则会添加到请求URL中

    Returns:
        Dict: 包含机器人、撇渣管、排渣堰门和套筒阀的状态信息的字典
    """
    try:
        # 从配置文件获取地址和端口
        robot_host = config.env['api']['development']['host']
        robot_port = config.env['api']['development']['port']
        
        headers = {
            "Accept": "application/json",
            "Connection": "keep-alive",
        }
        
        # 构建请求URL，如果提供了机器人ID则添加到查询参数中
        url = f"http://{robot_host}:{robot_port}/ctrl/api/device/run/info"
        if robot_id:
            url += f"?device_id={robot_id}"
        
        response = requests.get(
            url,
            headers=headers
        )
        response.raise_for_status()
        data = response.json()
        
        # 提取需要的字段
        result = {}
        if data.get("code") == 200:
            # 提取机器人状态
            if "robot" in data.get("data", {}):
                result["running_status"] = data["data"]["robot"].get("running_status")
                result["battery_percentage"] = data["data"]["robot"].get("battery_percentage")
                result["charging_status"] = data["data"]["robot"].get("charging_status")
                result["speed"] = data["data"]["robot"].get("speed")
                result["gear"] = data["data"]["robot"].get("gear")
                # 是否为ai控制
                result["ai_control_status"] = data["data"]["robot"].get("ai_control_status") 
            
            # 提取撇渣管状态
            if "slag_pipe" in data.get("data", {}):
                result["slag_pipe"] = data["data"]["slag_pipe"]
            
            # 提取排渣堰门状态
            if "slag_weir_gate" in data.get("data", {}):
                result["slag_weir_gate"] = data["data"]["slag_weir_gate"]
            
            # 提取套筒阀状态
            if "sleeve_valve" in data.get("data", {}):
                result["sleeve_valve"] = data["data"]["sleeve_valve"]
            logging.info('=' * 50)
            # 记录机器人状态日志
            if robot_id and result:
                running_status_text = "启动" if result.get("running_status") == 1 else "停止"
                ai_control_text = "开启" if result.get("ai_control_status") == 1 else "关闭"
                
                # 基本机器人状态
                logger.info(f"机器人{robot_id}，获取的状态：运行状态={running_status_text}，AI控制={ai_control_text}，电池电量={result.get('battery_percentage', 'unknown')}%")
                
                # 撇渣管状态
                if "slag_pipe" in result:
                    slag_pipe = result["slag_pipe"]
                    slag_ai_control = "开启" if slag_pipe.get("ai_control_status") == 1 else "关闭"
                    slag_open_status = "开启" if slag_pipe.get("open_valve_status") == 1 else "关闭"
                    logger.info(f"机器人{robot_id}，撇渣管状态：AI控制={slag_ai_control}，阀门状态={slag_open_status}")
                
                # 排渣堰门状态
                if "slag_weir_gate" in result:
                    weir_gate = result["slag_weir_gate"]
                    weir_ai_control = "开启" if weir_gate.get("ai_control_status") == 1 else "关闭"
                    weir_gate_status = "开启" if weir_gate.get("open_gate_status") == 1 else "关闭"
                    logger.info(f"机器人{robot_id}，排渣堰门状态：AI控制={weir_ai_control}，堰门状态={weir_gate_status}")
                
                # 套筒阀状态
                if "sleeve_valve" in result:
                    sleeve_valve = result["sleeve_valve"]
                    sleeve_ai_control = "开启" if sleeve_valve.get("ai_control_status") == 1 else "关闭"
                    sleeve_valve_status = "开启" if sleeve_valve.get("open_valve_status") == 1 else "关闭"
                    logger.info(f"机器人{robot_id}，套筒阀状态：AI控制={sleeve_ai_control}，阀门状态={sleeve_valve_status}")
            logging.info('=' * 50)
        return result
    except requests.exceptions.RequestException as e:
        logger.warning(f"获取机器人状态失败: {e}")
        return {}

# 获取机器人刹车状态
def get_robot_braking_status(robot_id: str) -> Dict:
    """获取机器人刹车状态
    
    Args:
        robot_id: 机器人ID
        
    Returns:
        Dict: 包含机器人刹车状态的字典，data=0表示未刹车，data=1表示正在刹车
    """
    try:
        # 从配置文件获取地址和端口
        robot_host = config.env['api']['development']['host']
        robot_port = config.env['api']['development']['port']
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        # 构建请求体
        payload = {
            "device_id": robot_id,
            "device_type": "robot",
            "action": "read_braking_status"
        }
        
        # 发送请求
        url = f"http://{robot_host}:{robot_port}/ctrl/api/device/control"
        logger.info(f"获取机器人刹车状态: 机器人ID={robot_id}, 请求URL={url}")
        
        response = requests.post(
            url,
            headers=headers,
            json=payload
        )
        response.raise_for_status()
        result = response.json()
        
        logger.info(f"获取机器人刹车状态成功: 机器人ID={robot_id}, 响应结果={json.dumps(result, ensure_ascii=False)}")
        return result
    except requests.exceptions.RequestException as e:
        error_msg = f"获取机器人刹车状态失败: 机器人ID={robot_id}, 错误信息={str(e)}"
        logger.error(error_msg)
        return {"error": str(e), "code": 500, "data": None}

# 机器人设备的反控



# 设备类型和帧率映射,30即30秒识别一次.
DEVICE_MAPPING = {
    # ====================== 在这里添加新的设备类型 ===================== #
    'system_prompt_filter1': {'frame_interval_all': 60*2 },                   # 滤池 | prompt_id: 1001
    'system_prompt_filter_multiple': {'frame_interval_all': 60*2 },           # 多图滤池 | prompt_id: 2001
    'system_prompt_aerobic_single1': {'frame_interval_all': 60*30 },        # 好氧池-前段 | prompt_id: 1002
    'system_prompt_aerobic_multiple1': { 'frame_interval_all': 60*30 },     # 多图好氧池-前段 | prompt_id: 2002
    'system_prompt_aerobic_single2': {'frame_interval_all': 60*30 },           # 好氧池-中段 | prompt_id: 1003
    'system_prompt_aerobic_multiple2': { 'frame_interval_all': 60*30 },     # 多图好氧池-中段 | prompt_id: 2003
    # ========================= 耙斗识别 ========================= #
    # 'new_device_type': {'result': 'new_result', 'frame_interval_all': new_interval},
    'system_prompt_bucket_dipper': {'frame_interval_all': 60*30},              # 耙斗-单图 | prompt_id: 1021
    'system_prompt_bucket_dipper_multiple': {'frame_interval_all': 60*30 }, # 多图耙斗 | prompt_id: 2021
    'system_prompt_bucket_shaft': {'frame_interval_all': 60*30 },              # 耙斗井 | prompt_id: 1322
    'system_prompt_bucket_shaft_multiple': {'frame_interval_all': 60*30},  # 多图耙斗井 | prompt_id: 2322

    # 泡沫检测
    'system_prompt_slag_outlet': {'frame_interval_all': 60*30},                # 排浮渣识别 | prompt_id: 1005
    # ======================= 二沉池分析 ======================= #
    'system_prompt_holistic_perspective': {'frame_interval_all': 60},       # 二沉池整体视角分析 | prompt_id: 1006
    'system_prompt_leaf_recognition': {'frame_interval_all': 60},           # 树叶识别 | prompt_id: 1007
    'system_prompt_slag_outletv2': {'frame_interval_all': 60},              # 排浮渣识别 | prompt_id: 1008
    'system_prompt_water_level_comparison': {'frame_interval_all': 60},     # 水量对比 | prompt_id: 1009
    # 二沉池机器人分析总提示词(集成了树叶识别、排浮渣识别、水量对比、青苔检测)
    'system_prompt_sedimentation_all': {'frame_interval_all': 60},          # 这个时间间隔没有作用 | prompt_id: 1031
    # 排渣堰门浮渣污泥监控分析
    'system_prompt_slag_weir_gate': {'frame_interval_all': 60},             # 排渣堰门浮渣污泥监控分析 | prompt_id: 1010
        # 青苔检测
    'system_prompt_moss': {'frame_interval_all': 60},                       # 青苔检测 | prompt_id: 1004
}
def process_device_id(prompt_id):
    # Function to identify the device and set the appropriate frame interval
    device_type = identify_device_id(prompt_id)
    
    if device_type in DEVICE_MAPPING:
        # result = DEVICE_MAPPING[device_type]['result']
        frame_interval_all = DEVICE_MAPPING[device_type]['frame_interval_all']
    else:
        # result = "未知设备"
        frame_interval_all = None

    return device_type, frame_interval_all

def identify_device_id(prompt_id):
    # 根据prompt_id识别设备类型,共四位数值:0000,第一位表示是单张图片推理还是多张图片推理,第二位表示是否一台设备含有多个提示词,第三位和第四位数值表示提示词的类型;暂定为01-19为大坦沙的,21-39为竹料的.
    patterns = {
        # ======================= 滤池和好氧池分析 ======================= #
        '1001': 'system_prompt_filter1', # 滤池
        '2001': 'system_prompt_filter_multiple', # 多图滤池
        
        '1002': 'system_prompt_aerobic_single1', # 单图好氧池-前段
        '2002': 'system_prompt_aerobic_multiple1', # 多图好氧池-前段
        '1003': 'system_prompt_aerobic_single2', # 单图好氧池-中段
        '2003': 'system_prompt_aerobic_multiple2', # 多图好氧池-中段
        
        '1005': 'system_prompt_slag_outlet', # 泡沫检测
        # ========================= 耙斗分析 ========================= #
        '1021': 'system_prompt_bucket_dipper', # 耙斗
        '2021': 'system_prompt_bucket_dipper_multiple', # 多图耙斗
        '1322': 'system_prompt_bucket_shaft', # 耙斗井
        '2322': 'system_prompt_bucket_shaft_multiple', # 多图耙斗井
        # ======================= 二沉池分析 ======================= #
        '1006': 'system_prompt_holistic_perspective', # 二沉池整体视角分析
        '1007': 'system_prompt_leaf_recognition', # 树叶识别
        '1008': 'system_prompt_slag_outletv2', # 排浮渣识别
        '1009': 'system_prompt_water_level_comparison', # 水量对比
        '1031': 'system_prompt_sedimentation_all', # sedimentation 全局四种识别
        '1010': 'system_prompt_slag_weir_gate', # 排渣堰门浮渣污泥监控分析
        '1004': 'system_prompt_moss', # 青苔检测
    }
    
    for pattern, type_place in patterns.items():
        if re.search(pattern, prompt_id):
            return type_place
    
    return '未知类型'

def process_device_data(data: Dict) -> list:
    """处理设备数据，构建摄像头配置列表"""
    camera_configs = []

    # 从配置文件获取字段key
    field_keys = config.env['device']['field_keys']
    BRAND_KEY = field_keys['brand']
    USERNAME_KEY = field_keys['username']
    PASSWORD_KEY = field_keys['password']
    IMAGE_KEY = field_keys['image']
    PROMPT_KEY = field_keys['prompt']

    for device in data.get("data", {}).get("rows", []):
        field_json = device.get("field_json", {})
        prompt_id = field_json.get(PROMPT_KEY)
        if prompt_id is not None and prompt_id != '-' and prompt_id != '':

            device_name = device.get("device_name")


            # 通过指定的key获取对应的值
            brand = field_json.get(BRAND_KEY)
            username = field_json.get(USERNAME_KEY)
            password = field_json.get(PASSWORD_KEY)
            image_path = field_json.get(IMAGE_KEY)
            
            result,frame_interval_all= process_device_id(prompt_id)
            system_type = result
            print(f"处理设备 {device.get('device_name')}:")
            print(f"品牌: {brand}, 用户名: {username}, 密码: {password}, 提示词: {prompt_id}")

            # 只有当用户名和密码都存在时才创建配置
            if username and password:
                # 根据品牌构建不同格式的RTSP地址
                if brand == "HIK":
                    rtsp_path = f"rtsp://{username}:{password}@{device.get('ip')}/Streaming/Channels/1"
                elif brand == "AHUA":
                    rtsp_path = f"rtsp://{username}:{password}@{device.get('ip')}:554/cam/realmonitor?channel=1&subtype=0"
                else:
                    print(f"警告：未知的摄像头品牌 {brand}，使用默认海康威视格式")
                    rtsp_path = f"rtsp://{username}:{password}@{device.get('ip')}/Streaming/Channels/1"
                
                # 构建video_path
                if image_path:
                    # 如果存在图片，构建包含图片和RTSP的列表
                    video_path = [
                        f"http://{api_config.host}:{api_config.port}{image_path}",
                        rtsp_path
                    ]
                    # if result == "滤池":
                    #     system_type = "system_prompt_filter1"
                    # elif result == "好氧池-前段":
                    #     system_type = "system_prompt_aerobic_multiple1"
                    # elif result == "好氧池-中段":
                    #     system_type = "system_prompt_aerobic_multiple2"
                else:
                    # 如果没有图片，只使用RTSP地址
                    # if result == "滤池":
                    #     system_type = "system_prompt_filter1"
                    # elif result == "好氧池-前段":
                    #     system_type = "system_prompt_aerobic_single1"
                    # elif result == "好氧池-中段":
                    #     system_type = "system_prompt_aerobic_single2" 
                    video_path = rtsp_path

                # if result == "滤池":
                #     frame_interval_all = 30*60
                # elif result == "好氧池-前段":
                #     frame_interval_all = 60*60
                # elif result == "好氧池-中段":
                #     frame_interval_all = 60*60

                camera_config = {
                    "video_path": video_path,
                    "camera_id": f"{device.get('device_id')}",
                    "video_id": device.get("e9_code"),
                    "threshold": 60.0, # 超过这个阈值的才会报警
                    "frame_interval_all": frame_interval_all,
                    "system_type": system_type,
                    "device_name": device_name, # 增加一个设备名称
                }
                camera_configs.append(camera_config)

    return camera_configs
# 处理机器人和其绑定的摄像头状态
def process_robot_and_cameras_status(data: Dict) -> Dict:
    """处理机器人和其绑定的摄像头状态
    
    Returns:
        Dict: 以父类机器人device_id为键，子类摄像头配置列表为值的字典
    """
    
    robot_camera_mapping = {}

    # 从配置文件获取字段key
    field_keys = config.env['device']['field_keys']
    BRAND_KEY = field_keys['brand']
    USERNAME_KEY = field_keys['username']
    PASSWORD_KEY = field_keys['password']
    IMAGE_KEY = field_keys['image']
    PROMPT_KEY = field_keys['prompt']
    DEVICE_LIST_KEY = field_keys['device_list']

    # 创建设备ID到设备信息的映射
    device_map = {}
    for device in data.get("data", {}).get("rows", []):
        device_map[str(device.get('device_id'))] = device

    for device in data.get("data", {}).get("rows", []):
        field_json = device.get("field_json", {})
        device_list = field_json.get(DEVICE_LIST_KEY) # 获取机器人绑定的摄像头字段
        prompt_id = field_json.get(PROMPT_KEY)
        device_id = str(device.get('device_id')) # 获取机器人id(摄像头的id就当作机器人的id)
        
        if device_list is not None : # 获取机器人绑定的摄像头列表
            if isinstance(device_list, str):
                device_ids = device_list.split(',')
            else:
                device_ids = device_list
            
            # 初始化该机器人的不同类型摄像头配置列表
            patrol_cameras = []  # 巡检摄像头
            weir_gate_cameras = []  # 堰门摄像头
            other_device_ids = []  # 其他设备ID
            
            # 遍历机器人绑定的摄像头列表
            for camera_device_id in device_ids:
                camera_device_id = camera_device_id.strip()  # 去除空格
                
                # 在data中查找对应的摄像头设备
                camera_device = device_map.get(camera_device_id)
                if camera_device is None:
                    # 如果在device_map中找不到对应的设备，说明可能是浓度计等其他设备
                    other_device_ids.append(camera_device_id)
                    continue
                    
                camera_field_json = camera_device.get("field_json", {})
                camera_prompt_id = camera_field_json.get(PROMPT_KEY)
                
                # 判断摄像头的提示词
                if camera_prompt_id is not None and camera_prompt_id != '-' and camera_prompt_id != '':
                    result, frame_interval_all = process_device_id(camera_prompt_id)
                    
                    # 通过指定的key获取摄像头设备的对应值
                    brand = camera_field_json.get(BRAND_KEY)
                    username = camera_field_json.get(USERNAME_KEY)
                    password = camera_field_json.get(PASSWORD_KEY)
                    image_path = camera_field_json.get(IMAGE_KEY)
                    
                    system_type = result
                    print(f"处理机器人 {device.get('device_name')} 绑定的摄像头 {camera_device.get('device_name')}:")
                    print(f"品牌: {brand}, 用户名: {username}, 密码: {password}, 提示词: {camera_prompt_id}, 系统类型: {system_type}")

                    # 只有当用户名和密码都存在时才创建配置
                    if username and password:
                        # 根据品牌构建不同格式的RTSP地址
                        if brand == "HIK":
                            rtsp_path = f"rtsp://{username}:{password}@{camera_device.get('ip')}/Streaming/Channels/1"
                        elif brand == "AHUA":
                            rtsp_path = f"rtsp://{username}:{password}@{camera_device.get('ip')}:554/cam/realmonitor?channel=1&subtype=0"
                        else:
                            print(f"警告：未知的摄像头品牌 {brand}，使用默认海康威视格式")
                            rtsp_path = f"rtsp://{username}:{password}@{camera_device.get('ip')}/Streaming/Channels/1"
                        
                        # 构建video_path
                        if image_path:
                            # 如果存在图片，构建包含图片和RTSP的列表
                            video_path = [
                                f"http://{api_config.host}:{api_config.port}{image_path}",
                                rtsp_path
                            ]
                        else:
                            video_path = rtsp_path
                            
                        camera_config = {
                            "video_path": video_path,
                            "camera_id": f"{camera_device.get('device_id')}",  # 使用摄像头的device_id
                            "video_id": camera_device.get("e9_code"),
                            "threshold": 60.0, # 超过这个阈值的才会报警
                            "frame_interval_all": frame_interval_all,
                            "system_type": system_type,
                            "device_name": camera_device.get('device_name', '')  # 添加设备名称
                        }
                        
                        # 根据系统类型分类摄像头
                        if system_type == 'system_prompt_sedimentation_all':
                            patrol_cameras.append(camera_config)
                            print(f"添加巡检摄像头: {camera_device.get('device_name')}")
                        elif system_type == 'system_prompt_slag_weir_gate':
                            weir_gate_cameras.append(camera_config)
                            print(f"添加堰门摄像头: {camera_device.get('device_name')}")
                        else:
                            # 其他类型的摄像头也添加到巡检摄像头列表中，但发出警告
                            logger.warning(f"发现未知类型的机器人绑定摄像头: {camera_device.get('device_name')} "
                                         f"(system_type: {system_type})，暂时归类为巡检摄像头。"
                                         f"请检查是否需要为其添加专门的处理逻辑。")
                            patrol_cameras.append(camera_config)
                            print(f"添加其他类型摄像头: {camera_device.get('device_name')}, 类型: {system_type}")
            
            # 如果该机器人有符合条件的摄像头或其他设备，则添加到映射字典中
            if patrol_cameras or weir_gate_cameras or other_device_ids:
                robot_camera_mapping[device_id] = {
                    "patrol_cameras": patrol_cameras,
                    "weir_gate_cameras": weir_gate_cameras,
                    "other_device_ids": other_device_ids
                }

    return robot_camera_mapping

def get_all_data_by_process_device_data(data: Dict) -> list:
    """返回所有，处理设备数据，构建摄像头配置列表"""
    camera_configs = []

    # 从配置文件获取字段key
    field_keys = config.env['device']['field_keys']
    BRAND_KEY = field_keys['brand']
    USERNAME_KEY = field_keys['username']
    PASSWORD_KEY = field_keys['password']
    IMAGE_KEY = field_keys['image']
    PROMPT_KEY = field_keys['prompt']

    for device in data.get("data", {}).get("rows", []):
        field_json = device.get("field_json", {})

        device_name = device.get("device_name")

        # 通过指定的key获取对应的值
        brand = field_json.get(BRAND_KEY)
        username = field_json.get(USERNAME_KEY)
        password = field_json.get(PASSWORD_KEY)
        image_path = field_json.get(IMAGE_KEY)
        print(f"处理设备 {device.get('device_name')}:")
        print(f"品牌: {brand}, 用户名: {username}, 密码: {password}")

        # 只有当用户名和密码都存在时才创建配置
        if username and password:
            # 根据品牌构建不同格式的RTSP地址
            if brand == "HIK":
                rtsp_path = f"rtsp://{username}:{password}@{device.get('ip')}/Streaming/Channels/1"
            elif brand == "AHUA":
                rtsp_path = f"rtsp://{username}:{password}@{device.get('ip')}:554/cam/realmonitor?channel=1&subtype=0"
            else:
                print(f"警告：未知的摄像头品牌 {brand}，使用默认海康威视格式")
                rtsp_path = f"rtsp://{username}:{password}@{device.get('ip')}/Streaming/Channels/1"

            # 构建video_path
            if image_path:
                # 如果存在图片，构建包含图片和RTSP的列表
                video_path = [
                    f"http://{api_config.host}:{api_config.port}{image_path}",
                    rtsp_path
                ]
            else:
                video_path = rtsp_path

            camera_config = {
                "video_path": video_path,
                "camera_id": f"{device.get('device_id')}",
                "video_id": device.get("e9_code")
            }
            camera_configs.append(camera_config)

    return camera_configs


def group_devices_by_scene(data: Dict) -> Dict:
    """
    根据sub_type字段将设备分组，相同sub_type的设备属于同一个场景
    
    Args:
        data (Dict): get_device_list()函数返回的原始数据
        
    Returns:
        Dict: 以sub_type为键，设备列表为值的字典
        {
            "scene_id": [
                {
                    "device_id": 4086,
                    "device_name": "5#二沉池",
                    "e9_code": "5#01",
                    "sub_type": "4d20d044f41a11efa38d0242ac120002",
                    "camera_id": "4086",
                    ...
                },
                ...
            ],
            ...
        }
    """
    scene_groups = {}
    
    try:
        # 获取设备列表
        devices = data.get("data", {}).get("rows", [])
        
        for device in devices:
            sub_type = device.get("sub_type")
            
            # 如果sub_type为空或者None，跳过该设备
            if not sub_type:
                logger.warning(f"设备 {device.get('device_name')} (ID: {device.get('device_id')}) 没有sub_type字段，跳过")
                continue
            
            # 如果该场景ID不存在，创建新的列表
            if sub_type not in scene_groups:
                scene_groups[sub_type] = []
            
            # 构建设备信息，添加camera_id字段以保持兼容性
            device_info = {
                "device_id": device.get("device_id"),
                "device_name": device.get("device_name"),
                "e9_code": device.get("e9_code"),
                "sub_type": sub_type,
                "camera_id": str(device.get("device_id")),  # 添加camera_id字段
                "main_type": device.get("main_type"),
                "ip": device.get("ip"),
                "port": device.get("port"),
                "field_json": device.get("field_json", {}),
                "create_time": device.get("create_time"),
                "update_time": device.get("update_time")
            }
            
            # 将设备添加到对应的场景组中
            scene_groups[sub_type].append(device_info)
        
        # 记录分组结果
        logger.info(f"设备分组完成，共分为 {len(scene_groups)} 个场景：")
        for scene_id, devices_list in scene_groups.items():
            logger.info(f"  场景 {scene_id}: {len(devices_list)} 台设备")
            for device in devices_list:
                logger.info(f"    - {device['device_name']} (ID: {device['device_id']})")
        
        return scene_groups
        
    except Exception as e:
        logger.error(f"设备分组失败: {e}")
        return {}

#  获取指定场景下的设备列表
def get_scene_devices_list(scene_id: str, data: Dict = None) -> list:
    """
    获取指定场景下的设备列表
    
    Args:
        scene_id (str): 场景ID (sub_type值)
        data (Dict, optional): get_device_list()函数返回的数据，如果为None则重新获取
        
    Returns:
        list: 场景下的设备列表
    """
    try:
        # 如果没有提供数据，则重新获取
        if data is None:
            data = get_device_list(main_type="cam")
            if not data:
                logger.error("获取设备列表失败")
                return []
        
        # 获取所有场景分组
        scene_groups = group_devices_by_scene(data)
        
        # 返回指定场景的设备列表
        scene_devices = scene_groups.get(scene_id, [])
        
        if not scene_devices:
            logger.warning(f"场景 {scene_id} 下没有找到设备")
        else:
            logger.info(f"场景 {scene_id} 下找到 {len(scene_devices)} 台设备")
        
        return scene_devices
        
    except Exception as e:
        logger.error(f"获取场景设备列表失败: {e}")
        return []

# 获取场景ID与中文名称的映射关系
def get_scene_name_mapping() -> Dict:
    """
    获取场景ID与中文名称的映射关系
    
    从 http://192.168.10.110:888/env-api/device/screen_all?scene=env_device&query_num=true&query_device=false
    接口获取场景中文名称映射
    
    Returns:
        Dict: 场景名称映射字典 {scene_id: scene_name}
        例如: {
            "4d20d044f41a11efa38d0242ac120002": "二沉池",
            "013663b8c26c11ef8e280242ac120002": "二沉池监控设备",
            "f0841c36c26b11efa62c0242ac120002": "滤池识别",
            "0c437d54c26c11ef8ca30242ac120002": "格栅间"
        }
    """
    try:
        # 接口配置
        base_url = f"{api_config.base_url}"
        endpoint = "/device/screen_all"
        
        # 查询参数
        params = {
            "scene": "env_device",
            "query_num": "true", 
            "query_device": "false"
        }
        
        # 认证Token (使用工作的token)
        token = "eyJhbGciOiJIUzUxMiJZiJ9-f0SPmOjeHvIMYu7xh2w"
        
        # 请求头设置
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
            "User-Agent": "Python-API-Client/1.0"
        }
        
        # 构建完整URL
        full_url = f"{base_url}{endpoint}"
        
        logger.info(f"获取场景名称映射: {full_url}")
        
        # 发送GET请求
        response = requests.get(
            url=full_url,
            params=params,
            headers=headers,
            timeout=30  # 30秒超时
        )
        
        # 检查HTTP状态码
        if response.status_code != 200:
            logger.error(f"请求失败，状态码: {response.status_code}")
            logger.error(f"错误信息: {response.text}")
            return {}
        
        # 解析JSON响应
        response_data = response.json()
        
        # 检查API响应中的code字段
        if response_data.get('code') != 200:
            logger.error(f"API请求失败: {response_data.get('msg', 'Unknown error')}")
            return {}
        else:
            logger.info("API请求成功，开始处理数据")
        # 获取实际数据
        data = response_data.get('data', [])
        scene_mapping = {}
        
        # 遍历主类型设备
        for main_device in data:
            if not isinstance(main_device, dict):
                continue
                
            main_type = main_device.get("dict_value")
            
            # 只处理监控设备（cam）类型
            if main_type == "cam":
                children = main_device.get("children", [])
                logger.info(f"找到监控设备，包含 {len(children)} 个子场景")
                
                # 遍历子类型（场景）
                for child in children:
                    scene_id = child.get("dict_value")  # 场景ID
                    scene_name = child.get("dict_label")  # 场景中文名称
                    
                    if scene_id and scene_name:
                        scene_mapping[scene_id] = scene_name
                        logger.info(f"场景映射: {scene_id} -> {scene_name}")
        
        logger.info(f"获取场景名称映射成功，共 {len(scene_mapping)} 个场景")
        return scene_mapping
        
    except requests.exceptions.Timeout:
        logger.error("获取场景名称映射失败 - 请求超时")
        return {}
    except requests.exceptions.ConnectionError:
        logger.error("获取场景名称映射失败 - 连接错误，请检查网络或服务器地址")
        return {}
    except requests.exceptions.RequestException as e:
        logger.error(f"获取场景名称映射失败 - 网络错误: {e}")
        return {}
    except json.JSONDecodeError as e:
        logger.error(f"获取场景名称映射失败 - JSON解析错误: {e}")
        return {}
    except Exception as e:
        logger.error(f"获取场景名称映射失败 - 未知错误: {e}")
        return {}

# 根据场景ID获取场景中文名称
def get_scene_name_by_id(scene_id: str) -> str:
    """
    根据场景ID获取场景中文名称
    
    Args:
        scene_id (str): 场景ID (sub_type值)
        
    Returns:
        str: 场景中文名称，如果没找到则返回默认名称
    """
    try:
        # 获取场景名称映射
        scene_mapping = get_scene_name_mapping()
        
        # 查找对应的中文名称
        scene_name = scene_mapping.get(scene_id)
        
        if scene_name:
            logger.info(f"场景 {scene_id} 对应的中文名称: {scene_name}")
            return scene_name
        else:
            # 如果没找到，返回默认名称
            default_name = f"场景{scene_id[:8]}"
            logger.warning(f"场景 {scene_id} 未找到对应的中文名称，使用默认名称: {default_name}")
            return default_name
            
    except Exception as e:
        logger.error(f"获取场景名称失败: {e}")
        # 出错时返回默认名称
        return f"场景{scene_id[:8]}"


def get_scene_devices_from_api(scene_id):
    """
    通过API获取场景下的设备列表
    
    Args:
        scene_id (str): 场景ID (sub_type)
        
    Returns:
        list: 设备列表 [{"camera_id": "4058", "device_name": "设备1", "sub_type": "scene_id"}, ...]
    """
    try:
        # 获取所有设备列表
        result = get_device_list(main_type="cam")
        if not result:
            logger.error("获取设备列表失败")
            return []
        
        # 使用新的分组函数获取指定场景的设备
        scene_devices = get_scene_devices_list(scene_id, result)
        
        if not scene_devices:
            logger.warning(f"场景 {scene_id} 下没有找到设备")
            return []
        
        # 转换为月报需要的格式
        formatted_devices = []
        for device in scene_devices:
            formatted_device = {
                "camera_id": str(device.get("device_id")),
                "device_name": device.get("device_name", f'设备{device.get("device_id")}'),
                "sub_type": device.get("sub_type"),
                "e9_code": device.get("e9_code"),
                "main_type": device.get("main_type"),
                "ip": device.get("ip"),
                "port": device.get("port")
            }
            formatted_devices.append(formatted_device)
        
        logger.info(f"场景 {scene_id} 获取到 {len(formatted_devices)} 台设备")
        for device in formatted_devices:
            logger.info(f"  - {device['device_name']} (camera_id: {device['camera_id']})")
        
        return formatted_devices
                
    except Exception as e:
        logger.error(f"通过API获取场景设备失败: {e}")
        return []


def get_device_scene_mapping():
    """
    获取设备与场景的映射关系，同时获取场景中文名称
    
    Returns:
        dict: 场景映射字典 {
            scene_id: {
                "scene_name": "场景中文名称",
                "devices": [{"camera_id": "xxx", "device_name": "xxx"}, ...]
            }
        }
    """
    try:
        # 1.通过API获取所有设备配置
        result = get_device_list(main_type="cam")
        if not result:
            logger.error("获取设备列表失败")
            return {}
        
        # 2.获取场景名称映射
        scene_name_mapping = get_scene_name_mapping()
        if not scene_name_mapping:
            logger.warning("获取场景名称映射失败，将使用默认名称")
        
        # 3.使用新的分组函数获取场景分组
        scene_groups = group_devices_by_scene(result)
        
        # 转换为月报需要的格式，包含场景名称
        scene_mapping = {}
        for scene_id, devices_list in scene_groups.items():
            # 获取场景中文名称
            scene_name = scene_name_mapping.get(scene_id, f"场景{scene_id[:8]}")  # 如果没有找到中文名称，使用默认名称
            
            scene_mapping[scene_id] = {
                "scene_name": scene_name,
                "devices": []
            }
            
            for device in devices_list:
                scene_mapping[scene_id]["devices"].append({
                    "camera_id": str(device.get("device_id")),
                    "device_name": device.get("device_name", f'设备{device.get("device_id")}'),
                    "sub_type": device.get("sub_type"),
                    "e9_code": device.get("e9_code"),
                    "main_type": device.get("main_type"),
                    "ip": device.get("ip"),
                    "port": device.get("port")
                })
        
        logger.info(f"获取到 {len(scene_mapping)} 个场景的设备映射关系")
        for scene_id, scene_info in scene_mapping.items():
            logger.info(f"场景 {scene_id} ({scene_info['scene_name']}): {len(scene_info['devices'])} 台设备")
        
        return scene_mapping
        
    except Exception as e:
        logger.error(f"获取设备场景映射失败: {e}")
        return {}


if __name__ == "__main__":
    # 获取摄像头设备
    # result = get_device_list(main_type="cam")
    # 处理数据并构建配置
    # camera_configs = process_device_data(result)
    # 打印结果
    # print("CAMERA_CONFIGS = ", json.dumps(camera_configs, indent=2, ensure_ascii=False))
    
    # 查找场景和设备接口的映射
    

    # 测试获取机器人的状态
    # robot_status = get_robot_status(robot_id="4048")
    # print("ROBOT_STATUS = ", json.dumps(robot_status, indent=2, ensure_ascii=False))
    
    # 测试获取机器人刹车状态
    # braking_status = get_robot_braking_status(robot_id="4048")
    # print("ROBOT_BRAKING_STATUS = ", json.dumps(braking_status, indent=2, ensure_ascii=False))
    
    # 测试使用设备ID获取机器人状态
    # robot_status_with_id = get_robot_status(device_id="4234")
    # print("ROBOT_STATUS (设备ID: 4234) = ", json.dumps(robot_status_with_id, indent=2, ensure_ascii=False))
    # 测试控制机器人
    # control_robot("slag_pipe","switch_to_ai_control-ai","4234","open_valve")
    # control_robot("slag_pipe","switch_to_ai_control-ai","4234","close_valve")
    # 测试获取机器人和其绑定的摄像头状态
    # camera_configs_all = process_robot_and_cameras_status(result)
    # print("CAMERA_CONFIGS_ALL = ", json.dumps(camera_configs_all, indent=2, ensure_ascii=False))
    
    # # 测试新的返回格式
    # for robot_id, device_data in camera_configs_all.items():
    #     print(f"\n机器人ID: {robot_id}")
    #     print(f"摄像头配置数量: {len(device_data['camera_configs'])}")
    #     print(f"其他设备ID列表: {device_data['other_device_ids']}")

    # ----------------------------------- 月报场景 ----------------------------------- #
    get_scene_name_mapping()

